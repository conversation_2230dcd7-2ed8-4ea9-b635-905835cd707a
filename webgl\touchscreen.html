<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8">
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8">
    <title>桂林智源 SVG 数字化系统 - 触摸屏版本</title>
    <link rel="shortcut icon" href="logo.png">
    <!-- 引入字体图标 -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">

    <style>
        /**
         * 桂林智源 SVG 数字化系统 - 触摸屏版本样式
         * 专为1920×1080触摸屏优化设计
         * 深色科技主题，工业监控界面风格
         */

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #00d4ff;
            --secondary-color: #0099cc;
            --accent-color: #00ff88;
            --warning-color: #ffaa00;
            --error-color: #ff4444;
            --success-color: #00ff88;
            --bg-primary: #0a0f1c;
            --bg-secondary: #1a2332;
            --bg-tertiary: #2a3441;
            --text-primary: #ffffff;
            --text-secondary: #b8c5d6;
            --border-color: #3a4a5c;
            --shadow-color: rgba(0, 212, 255, 0.3);
        }

        body {
            font-family: 'Microsoft YaHei', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, var(--bg-primary) 0%, var(--bg-secondary) 100%);
            color: var(--text-primary);
            width: 1920px;
            height: 1080px;
            overflow: hidden;
            position: relative;
            user-select: none; /* 触摸屏优化：禁用文本选择 */
            -webkit-touch-callout: none; /* 禁用长按菜单 */
        }

        /* 背景动画效果 */
        body::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(0, 212, 255, 0.1) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(0, 255, 136, 0.1) 0%, transparent 50%);
            pointer-events: none;
            z-index: 0;
        }

        /* 主容器 */
        .touchscreen-container {
            position: relative;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            z-index: 1;
        }

        /* 顶部标题栏 */
        .header {
            height: 120px;
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            border-bottom: 2px solid var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 40px;
            box-shadow: 0 4px 20px rgba(0, 212, 255, 0.3);
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 30px;
        }

        .logo {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .logo-image {
            width: 60px;
            height: 60px;
            border-radius: 8px;
        }

        .logo-text {
            font-size: 28px;
            font-weight: bold;
            color: var(--primary-color);
        }

        .system-title {
            font-size: 32px;
            font-weight: bold;
            color: var(--text-primary);
            text-shadow: 0 2px 10px rgba(0, 212, 255, 0.5);
        }

        .header-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .time-display {
            font-size: 24px;
            color: var(--accent-color);
            font-weight: bold;
            text-shadow: 0 2px 10px rgba(0, 255, 136, 0.5);
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            padding: 30px;
            gap: 30px;
        }

        /* 功能模块网格 */
        .modules-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            grid-template-rows: repeat(3, 1fr);
            gap: 30px;
            flex: 1;
        }

        /* 功能模块按钮 */
        .module-btn {
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            border: 2px solid var(--border-color);
            border-radius: 20px;
            color: var(--text-primary);
            cursor: pointer;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 20px;
            font-size: 24px;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
            position: relative;
            overflow: hidden;
            /* 触摸屏优化 */
            min-height: 180px;
            touch-action: manipulation;
        }

        .module-btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }

        .module-btn:hover::before,
        .module-btn:active::before {
            left: 100%;
        }

        .module-btn:hover,
        .module-btn:active {
            border-color: var(--primary-color);
            box-shadow: 0 12px 35px rgba(0, 212, 255, 0.4);
            transform: translateY(-5px) scale(1.02);
        }

        .module-btn:active {
            transform: translateY(-2px) scale(0.98);
            box-shadow: 0 6px 20px rgba(0, 212, 255, 0.6);
        }

        .module-icon {
            font-size: 48px;
            color: var(--primary-color);
            text-shadow: 0 2px 10px rgba(0, 212, 255, 0.5);
        }

        .module-text {
            font-size: 20px;
            text-align: center;
            line-height: 1.2;
        }

        /* 底部状态栏 */
        .footer {
            height: 80px;
            background: linear-gradient(135deg, var(--bg-secondary), var(--bg-tertiary));
            border-top: 2px solid var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 40px;
            font-size: 18px;
        }

        .footer-left {
            color: var(--text-secondary);
        }

        .footer-right {
            color: var(--text-secondary);
        }

        /* 弹窗样式 */
        .modal-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            opacity: 0;
            visibility: hidden;
            transition: all 0.3s ease;
        }

        .modal-overlay.show {
            opacity: 1;
            visibility: visible;
        }

        .modal-container {
            width: 1920px;
            height: 1080px;
            background: var(--bg-primary);
            border: 2px solid var(--primary-color);
            border-radius: 20px;
            position: relative;
            overflow: hidden;
            box-shadow: 0 20px 60px rgba(0, 212, 255, 0.3);
        }

        .modal-header {
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            height: 100px;
            background: transparent;
            border: none;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 20px;
            z-index: 1001;
            pointer-events: none; /* 让背景区域不阻挡点击 */
        }

        .modal-controls {
            display: flex;
            justify-content: space-between;
            width: 100%;
            pointer-events: auto; /* 恢复按钮的点击功能 */
        }

        .modal-controls .modal-btn:first-child {
            /* 返回按钮在左侧 */
            order: 1;
        }

        .modal-controls .modal-btn:last-child {
            /* 关闭按钮在右侧 */
            order: 2;
        }

        .modal-btn {
            width: 60px;
            height: 60px;
            background: linear-gradient(135deg, rgba(42, 52, 65, 0.9), rgba(26, 35, 50, 0.9));
            border: 2px solid var(--border-color);
            border-radius: 12px;
            color: var(--text-primary);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            transition: all 0.3s ease;
            touch-action: manipulation;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
        }

        .modal-btn:hover,
        .modal-btn:active {
            background: linear-gradient(135deg, rgba(0, 212, 255, 0.2), rgba(0, 153, 204, 0.2));
            border-color: var(--primary-color);
            color: var(--primary-color);
            box-shadow: 0 6px 25px rgba(0, 212, 255, 0.4);
            transform: scale(1.05);
        }

        .modal-btn:active {
            transform: scale(0.95);
            box-shadow: 0 2px 15px rgba(0, 212, 255, 0.6);
        }

        .modal-content {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .modal-iframe {
            width: 100%;
            height: 100%;
            border: none;
            background: var(--bg-primary);
        }

        /* 响应式优化 */
        @media (max-width: 1920px) {
            body {
                width: 100vw;
                height: 100vh;
            }

            .modal-container {
                width: 100vw;
                height: 100vh;
                border-radius: 0;
            }
        }
    </style>
</head>
<body>
    <div class="touchscreen-container">
        <!-- 顶部标题栏 -->
        <header class="header">
            <div class="header-left">
                <div class="logo">
                    <img src="logo.png" alt="桂林智源" class="logo-image">
                    <span class="logo-text">桂林智源</span>
                </div>
                <div class="system-title">SVG 数字化系统 - 触摸屏版本</div>
            </div>
            <div class="header-right">
                <div class="time-display" id="currentTime"></div>
            </div>
        </header>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 功能模块网格 -->
            <div class="modules-grid">
                <!-- 第一行 -->
                <button class="module-btn" onclick="openModule('electrical-topology')">
                    <i class="fas fa-bolt module-icon"></i>
                    <span class="module-text">电气拓扑</span>
                </button>

                <button class="module-btn" onclick="openModule('cooling-topology')">
                    <i class="fas fa-tint module-icon"></i>
                    <span class="module-text">水冷拓扑</span>
                </button>

                <button class="module-btn" onclick="openModule('io-status')">
                    <i class="fas fa-plug module-icon"></i>
                    <span class="module-text">I/O状态</span>
                </button>

                <button class="module-btn" onclick="openModule('unit-status')">
                    <i class="fas fa-microchip module-icon"></i>
                    <span class="module-text">单元状态</span>
                </button>

                <!-- 第二行 -->
                <button class="module-btn" onclick="openModule('master-control')">
                    <i class="fas fa-sitemap module-icon"></i>
                    <span class="module-text">主控辅控</span>
                </button>

                <button class="module-btn" onclick="openModule('debug-params-1')">
                    <i class="fas fa-cogs module-icon"></i>
                    <span class="module-text">调试参数1</span>
                </button>

                <button class="module-btn" onclick="openModule('debug-params-2')">
                    <i class="fas fa-tools module-icon"></i>
                    <span class="module-text">调试参数2</span>
                </button>

                <button class="module-btn" onclick="openModule('history-event')">
                    <i class="fas fa-calendar-alt module-icon"></i>
                    <span class="module-text">历史事件</span>
                </button>

                <!-- 第三行 -->
                <button class="module-btn" onclick="openModule('parameter-curve')">
                    <i class="fas fa-chart-area module-icon"></i>
                    <span class="module-text">参数曲线</span>
                </button>

                <button class="module-btn" onclick="openModule('dsp')">
                    <i class="fas fa-microchip module-icon"></i>
                    <span class="module-text">DSP</span>
                </button>

                <button class="module-btn" onclick="openModule('fault-wave')">
                    <i class="fas fa-wave-square module-icon"></i>
                    <span class="module-text">故障录波</span>
                </button>

                <button class="module-btn" onclick="openModule('version-info')">
                    <i class="fas fa-info-circle module-icon"></i>
                    <span class="module-text">版本信息</span>
                </button>
            </div>
        </main>

        <!-- 底部状态栏 -->
        <footer class="footer">
            <div class="footer-left">
                <span>系统版本: v2.1.0 | 最后更新: 2025-06-24 14:30:25</span>
            </div>
            <div class="footer-right">
                <span>© 2025 桂林智源 - SVG 数字化系统</span>
            </div>
        </footer>
    </div>

    <!-- 功能模块弹窗 -->
    <div id="moduleModal" class="modal-overlay">
        <div class="modal-container">
            <div class="modal-header">
                <div class="modal-controls">
                    <button class="modal-btn" onclick="goBack()" title="返回上一页">
                        <i class="fas fa-arrow-left"></i>
                    </button>
                    <button class="modal-btn" onclick="closeModal()" title="关闭">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
            </div>
            <div class="modal-content">
                <iframe id="moduleIframe" class="modal-iframe" src=""></iframe>
            </div>
        </div>
    </div>

    <script>
        /**
         * 桂林智源 SVG 数字化系统 - 触摸屏版本 JavaScript
         * 功能模块管理和弹窗控制
         */

        // 模块配置映射
        const moduleConfig = {
            'electrical-topology': {
                title: '电气系统拓扑图',
                icon: 'fas fa-bolt',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=6f2b118e-1b27-4ebd-b1db-bcd08ce10bbf&type=3&date='
            },
            'cooling-topology': {
                title: '水冷系统拓扑图',
                icon: 'fas fa-tint',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=bdd07113-f2fd-4744-88f0-a055916c976b&type=3&date='
            },
            'io-status': {
                title: 'I/O状态监控',
                icon: 'fas fa-plug',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=df533b38-98b3-4c1a-9a3a-b2d7884f7770&type=3&date='
            },
            'unit-status': {
                title: '单元状态监控',
                icon: 'fas fa-microchip',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=bc305d60-29d2-4635-82bb-ead9b337b31d&type=3&wework_cfm_code=NOhs%2BuVWHo97Du860XjXIPC5tyE8DwbeJo3xoLtc8tn94QGaXR9LJW5VvFSCVJID5Fpwj6f%2FVjFU6LVqrXItFhckh8qcSUUqInRO3%2Fb3FD0Ee6bfED2vOqLVG6i2ymNIFQ7%2Fi%2BDxy8I7Xi5S1uPRzyxBjWWrv5w9p218BH1F2vIV&date='
            },
            'master-control': {
                title: '主控|辅控',
                icon: 'fas fa-sitemap',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=6f1379ce-d7b5-4017-9d9d-78d49813cd8c&type=3&date='
            },
            'debug-params-1': {
                title: '调试参数1',
                icon: 'fas fa-cogs',
                url: '调试参数1.html'
            },
            'debug-params-2': {
                title: '调试参数2',
                icon: 'fas fa-tools',
                url: '调试参数2.html'
            },
            'history-event': {
                title: '历史事件',
                icon: 'fas fa-calendar-alt',
                url: '历史事件.html'
            },
            'parameter-curve': {
                title: '参数曲线',
                icon: 'fas fa-chart-area',
                url: '参数曲线.html'
            },
            'dsp': {
                title: 'DSP',
                icon: 'fas fa-microchip',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=721de54b-bed8-43dc-9157-81ac6cff32a4&type=3&date='
            },
            'fault-wave': {
                title: '故障录波',
                icon: 'fas fa-wave-square',
                url: '故障录波.html'
            },
            'version-info': {
                title: '版本信息',
                icon: 'fas fa-info-circle',
                url: 'https://mqtt.qizhiyun.cc/scada/topo/fullscreen?guid=eb900ac1-737c-4610-b4b3-ea05239531e3&type=3&date='
            }
        };

        /**
         * 打开功能模块
         * @param {string} moduleId - 模块ID
         */
        function openModule(moduleId) {
            console.log(`打开模块: ${moduleId}`);

            const config = moduleConfig[moduleId];
            if (!config) {
                console.error(`未找到模块配置: ${moduleId}`);
                return;
            }

            // 获取弹窗元素
            const moduleIframe = document.getElementById('moduleIframe');
            const modalOverlay = document.getElementById('moduleModal');

            // 构建URL（如果需要添加时间戳）
            let url = config.url;
            if (url.includes('mqtt.qizhiyun.cc')) {
                url += new Date().getTime();
            }

            // 设置iframe源
            moduleIframe.src = url;

            // 显示弹窗
            modalOverlay.classList.add('show');

            // 添加触摸屏优化的事件监听
            addTouchOptimization();

            console.log(`已打开模块: ${config.title}`);
        }

        /**
         * 关闭弹窗
         */
        function closeModal() {
            console.log('关闭弹窗');
            const modalOverlay = document.getElementById('moduleModal');
            const moduleIframe = document.getElementById('moduleIframe');

            modalOverlay.classList.remove('show');

            // 清空iframe源以释放资源
            setTimeout(() => {
                moduleIframe.src = '';
            }, 300);
        }

        /**
         * 返回上一页（iframe内部）
         */
        function goBack() {
            console.log('返回上一页');
            const moduleIframe = document.getElementById('moduleIframe');

            try {
                // 尝试让iframe返回上一页
                moduleIframe.contentWindow.history.back();
            } catch (error) {
                console.warn('无法访问iframe历史记录，可能是跨域限制');
                // 如果无法访问iframe历史，则关闭弹窗
                closeModal();
            }
        }

        /**
         * 添加触摸屏优化
         */
        function addTouchOptimization() {
            // 防止双击缩放
            let lastTouchEnd = 0;
            document.addEventListener('touchend', function (event) {
                const now = (new Date()).getTime();
                if (now - lastTouchEnd <= 300) {
                    event.preventDefault();
                }
                lastTouchEnd = now;
            }, false);

            // 防止长按选择文本
            document.addEventListener('selectstart', function(e) {
                e.preventDefault();
            });
        }

        /**
         * 更新时间显示
         */
        function updateTime() {
            const now = new Date();
            const timeString = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit',
                hour12: false
            });

            const timeDisplay = document.getElementById('currentTime');
            if (timeDisplay) {
                timeDisplay.textContent = timeString;
            }
        }

        /**
         * 键盘事件处理（ESC键关闭弹窗）
         */
        function handleKeyboard(event) {
            if (event.key === 'Escape') {
                closeModal();
            }
        }

        /**
         * 初始化页面
         */
        function initializePage() {
            console.log('初始化触摸屏版本页面');

            // 更新时间显示
            updateTime();
            setInterval(updateTime, 1000);

            // 添加键盘事件监听
            document.addEventListener('keydown', handleKeyboard);

            // 添加触摸屏优化
            addTouchOptimization();

            // 点击弹窗外部关闭弹窗
            const modalOverlay = document.getElementById('moduleModal');
            modalOverlay.addEventListener('click', function(event) {
                if (event.target === modalOverlay) {
                    closeModal();
                }
            });

            console.log('触摸屏版本页面初始化完成');
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', initializePage);

        // 页面可见性变化时的处理
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'visible') {
                updateTime();
            }
        });
    </script>
</body>
</html>